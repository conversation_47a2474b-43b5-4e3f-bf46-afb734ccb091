<?php

namespace Modules\Account\app\Filament\Resources;

use App\Forms\ChangePasswordForm;
use App\Shared\Helpers\FormComponents;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Filament\Resources\AccountsResource\Pages\CreateAccounts;
use Modules\Account\app\Filament\Resources\AccountsResource\Pages\EditAccounts;
use Modules\Account\app\Filament\Resources\AccountsResource\Pages\ListAccounts;
use Modules\Account\app\Filament\Resources\AccountsResource\Pages\ViewAccounts;
use Modules\Account\app\Filament\Resources\AccountsResource\RelationManagers\LeaseRelationManager;
use Modules\Account\app\Filament\Resources\AccountsResource\RelationManagers\PropertiesRelationManager;
use Modules\Account\app\Models\Account;
use Modules\Account\Services\AccountCustomerService;
use Modules\BankAccount\Forms\BankAccountForm;
use Closure;
use Modules\Account\app\Filament\Resources\AccountsResource\Widgets\AccountsOverview;
use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;

class AccountsResource extends Resource
{
    protected static ?string $model = Account::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        $updateFullName = function ($set, $get) {
            $set('name', trim(
                $get('first_name') . ' ' .
                $get('second_name') . ' ' .
                $get('third_name') . ' ' .
                $get('last_name')
            ));
        };
        return $form
            ->schema([
                Forms\Components\Hidden::make('name'),
                Forms\Components\TextInput::make('first_name')
                    ->required()
                    ->label(__('First Name'))
                    ->placeholder(__('Enter first name'))
                    ->afterStateUpdated($updateFullName),
                Forms\Components\TextInput::make('second_name')
                    ->required()
                    ->label(__('Second Name'))
                    ->placeholder(__('Enter second name'))
                    ->afterStateUpdated($updateFullName),
                Forms\Components\TextInput::make('third_name')
                    ->required()
                    ->label(__('Third Name'))
                    ->placeholder(__('Enter third name'))
                    ->afterStateUpdated($updateFullName),
                Forms\Components\TextInput::make('last_name')
                    ->required()
                    ->label(__('Last Name'))
                    ->placeholder(__('Enter last name'))
                    ->afterStateUpdated($updateFullName),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->unique(table: 'accounts',column: 'email',ignoreRecord:true)
                    ->label(__('Email'))
                    ->placeholder(__('Enter email address'))
                    ->validationMessages([
                        'required' => __('Email is required'),
                        'email' => __('Please enter a valid email address'),
                        'unique' => __('This email is already taken'),
                    ]),

                Forms\Components\TextInput::make('phone')
                    ->label(__('Phone Number'))
                    ->tel()
                    ->required()
                    ->prefix('+966')
                    ->placeholder('5xxxxxxxx')
                    ->rules([
                        'required',
                        'string',
                        'regex:/^5[0-9]{8}$/',
                        'size:9',
                        fn ($record): Closure => function (string $attribute, $value, Closure $fail) use ($record) {
                            $fullNumber = '+966' . $value;
                            $query = Account::where('phone', $fullNumber);

                            if ($record) {
                                $query->where('id', '!=', $record->id);
                            }

                            if ($query->exists()) {
                                $fail(__('This phone number is already taken'));
                            }
                        },
                    ])
                    ->extraInputAttributes([
                        'maxlength' => '9',
                        'pattern' => '5[0-9]*',
                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "").substring(0, 9)'
                    ])
                    ->dehydrateStateUsing(fn ($state) => '+966' . $state)
                    ->formatStateUsing(fn ($state) => str_replace('+966', '', $state))
                    ->validationMessages([
                        'required' => __(' phone is required'),
                        'regex' => __('Phone number must start with 5 and be 9 digits'),
                        'size' => __('Phone number must be exactly 9 digits'),
                        'unique' => __('This phone number is already registered'),
                    ]),

                FormComponents::idFieldSet(),


                FormComponents::birthDateInput('birth_date')
                    ->label(__('Birth Date')),

                Forms\Components\Select::make('lang')
                    ->label(__('Preferred Language'))
                    ->placeholder(__('Select language'))
                    ->options([
                         'en' => __('English'),
                         'ar' => __('Arabic'),
                    ])
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->label(__('Active'))
                    ->onColor('success')
                    ->offColor('danger')
                    ->required(),

                ...ChangePasswordForm::make($form->getOperation(), true),


                Forms\Components\Repeater::make('accountRoles')
                    ->relationship()
                    ->schema([
                        Forms\Components\Select::make('role')
                            ->label(__('Role'))
                            ->placeholder(__('Select role'))
                            ->options([
                                'tenant' => __('Tenant'),
                                'owner' => __('Owner'),
                            ])
                            ->required(),
                        Forms\Components\Toggle::make('is_default')
                            ->label(__('Is Default Role'))
                            ->default(true),
                    ])
                    ->maxItems(1)
                    ->minItems(1)
                    ->defaultItems(1)
                    ->columnSpanFull()
                    ->deletable(false)
                    ->hiddenLabel()
                    ->visible(fn ($context) => $context === 'create'),

                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Repeater::make('bankAccounts')
                            ->label(__('Bank Accounts'))
                            ->relationship('bankAccounts')
                            ->schema(BankAccountForm::make())
                            ->columnSpan('full')
                            ->deleteAction(
                                fn(Action $action) => $action
                                    ->requiresConfirmation()
                                    ->modalHeading(__('Delete Bank Account'))
                                    ->modalDescription(__('Are you sure you want to delete this bank account?'))
                                    ->before(function ($record, $state, $arguments) use ($action) {
                                        if (isset($state[$arguments['item']]['id'])) {
                                            $isInUse = DB::table('lease_members')
                                                ->where('bank_account_id', $state[$arguments['item']]['id'])
                                                ->whereExists(function ($query) {
                                                    $query->select(DB::raw(1))
                                                        ->from('leases')
                                                        ->whereRaw('leases.id = lease_members.lease_id')
                                                        ->whereNull('leases.deleted_at');
                                                })
                                                ->exists();

                                            if ($isInUse) {
                                                Notification::make()
                                                    ->danger()
                                                    ->title(__('Delete Failed'))
                                                    ->body(__('This bank account is currently in use by an active lease and cannot be deleted.'))
                                                    ->send();
                                                $action->cancel();
                                            }
                                        }
                                    })
                            )
                    ]),

                Card::make()
                    ->schema([
                        Toggle::make('enable_vat')
                            ->label(__('enable vat number'))
                            ->reactive() // Ensure this is reactive so changes are tracked
                            ->afterStateHydrated(function ($state, $set, $get, $record) {
                                if ($record && ($record->vat_number ?? 0) > 0) {
                                    $set('enable_vat', true);
                                }
                            })
                            ->afterStateUpdated(function ($state, $set) {
                                if (!$state) {
                                    $set('vat_number', 0);
                                }
                            }),


                        TextInput::make('vat_number')
                            ->label(__('vat number'))
                            ->numeric()
                            ->required(function (Get $get): bool {
                                return $get('enable_vat') === true;
                            })
                            ->default(0)
                            ->minValue(0)
                            ->visible(function (Get $get): bool {
                                return $get('enable_vat') === true;
                            })
                            ->afterStateUpdated(function ($set, $state, Get $get) {
                                if (! $get('enable_vat')) {
                                    $set('vat_number', 0);
                                }
                            })
                            ->validationMessages([
                                'required' => __('VAT Number is required'),
                            ]),
                    ])
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->when(request('search'), function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('first_name', 'like', "%{$search}%")
                        ->orWhere('second_name', 'like', "%{$search}%")
                        ->orWhere('third_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhereRaw("CONCAT(first_name, ' ', second_name, ' ', third_name, ' ', last_name) LIKE ?", ["%{$search}%"]);
                });
            });

        // Filter accounts by company for non-super admin users
        $user = auth()->user();
        if ($user && !$user->hasRole('super_admin') && $user->company_id) {
            $query->where('company_id', $user->company_id);
        }

        return $query;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->getStateUsing(fn ($record) => $record->first_name . ' ' . $record->second_name . ' ' . $record->third_name . ' ' . $record->last_name)
                    ->sortable(),
                Tables\Columns\TextColumn::make('company.name')
                    ->label(__('Company'))
                    ->searchable()
                    ->sortable()
                    ->placeholder(__('No Company'))
                    ->visible(fn () => auth()->user()?->hasRole('super_admin')),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('Email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label(__('Phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('birth_date')
                    ->label(__('Birth Date'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('lang')
                    ->label(__('Language'))
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label(__('Active Status'))
                    ->onColor('success')
                    ->offColor('danger')
                    ->sortable()
                    ->action(function ($record) {
                        $record->is_active = !$record->is_active;
                        $record->save();
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label(__('Deleted at'))
                    ->since()
                    ->sortable()
                    ->tooltip(fn ($record) => $record->deleted_at?->format('Y-m-d H:i:s'))
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('is_active')
                    ->label(__('Active Status'))
                    ->options([
                        true => __('Active'),
                        false => __('Inactive'),
                    ])->native(false),
                SelectFilter::make('id_type')
                    ->label(__('ID Type'))
                    ->options([
                        'national_id' => __('National ID'),
                        'residency_permit' => __('Residency Permit'),
                        'passport' => __('Passport'),
                        'gcc_id' => __('GCC Citizen ID'),
                        'other' => __('Other'),
                    ])
                    ->native(false),
                Tables\Filters\Filter::make('id_number')
                    ->form([
                        Forms\Components\TextInput::make('id_number')->label(__('ID Number')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['id_number'],
                                fn (Builder $query, $value): Builder => $query->where('national_id', 'like', "%{$value}%")
                            );
                    }),
                Tables\Filters\Filter::make('name')
                    ->form([
                        Forms\Components\TextInput::make('name')->label(__('Name')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['name'],
                                fn (Builder $query, $name): Builder => $query->where(function ($query) use ($name) {
                                    $query->where('first_name', 'like', "%{$name}%")
                                        ->orWhere('second_name', 'like', "%{$name}%")
                                        ->orWhere('third_name', 'like', "%{$name}%")
                                        ->orWhere('last_name', 'like', "%{$name}%");
                                })
                            );
                    }),

                Tables\Filters\Filter::make('birth_date')
                    ->form([
                        Forms\Components\DatePicker::make('birth_date_from')->label(__('Birth Date From')),
                        Forms\Components\DatePicker::make('birth_date_to')->label(__('Birth Date To')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['birth_date_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('birth_date', '>=', $date)
                            )
                            ->when(
                                $data['birth_date_to'],
                                fn (Builder $query, $date): Builder => $query->whereDate('birth_date', '<=', $date)
                            );
                    }),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')->label(__('Created From')),
                        Forms\Components\DatePicker::make('created_until')->label(__('Created Until')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date)
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date)
                            );
                    }),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->requiresConfirmation()
                        ->action(function (Account $record) {
                            $accountCustomerService = app(\Modules\Account\Services\AccountCustomerService::class);
                            if ($accountCustomerService->deleteAccount($record->id)) {
                                // Show success notification
                                \Filament\Notifications\Notification::make()
                                    ->title('Success')
                                    ->body(__('The account has been deleted successfully'))
                                    ->success()
                                    ->send();
                            } else {
                                // Show error notification
                                \Filament\Notifications\Notification::make()
                                    ->title('Error')
                                    ->body(__('The account is related to leases and cannot be deleted'))
                                    ->danger()
                                    ->send();
                            }
                        }),
                    Tables\Actions\Action::make('sendNewPassword')
                        ->label(__('Send New Password'))
                        ->icon('heroicon-o-key')
                        ->modalHeading(__('Send New Password'))
                        ->requiresConfirmation()
                        ->modalButton(__('Send'))
                        ->action(function (Account $record): void {
                            // Generate random password
                            $lowerCaseLetters = 'abcdefghijklmnopqrstuvwxyz';
                            $upperCaseLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                            $numbers = '**********';
                            $specialChars = '!@#$%^&*()._-';
                            $newPassword = substr(str_shuffle($lowerCaseLetters), 0, 4) .
                                substr(str_shuffle($upperCaseLetters), 0, 2) .
                                $specialChars[rand(0, strlen($specialChars) - 1)] .
                                substr(str_shuffle($numbers), 0, 5);
                            $newPassword = str_shuffle($newPassword);
                            $record->update([
                                'password' => Hash::make($newPassword),
                            ]);
                            $template = NotificationsTemplate::where(['key' => 'send_user_credential'])->first();
                            if ($template) {
                                SendNotification::make(['email'])
                                    ->template($template->key)
                                    ->model(Account::class)
                                    ->id($record->id)
                                    ->findBody(['{username}', '{email}', '{password}'])
                                    ->replaceBody([$record->name, $record->national_id, $newPassword])
                                    ->icon($template->icon)
                                    ->lang($record->lang)
                                    ->url(url($template->url))
                                    ->privacy('private')
                                    ->database(true)
                                    ->fire();
                            }
                        }),
                ])
                    ->button()
                    ->extraAttributes([
                        'class' => 'custom-action-btn',
                    ])
                    ->color('transparent')
                    ->label(__('Commends')),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ;
    }

    public static function getRelations(): array
    {
        return [
            PropertiesRelationManager::class,
            LeaseRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListAccounts::route('/'),
            'create' => CreateAccounts::route('/create'),
            'edit' => EditAccounts::route('/{record}/edit'),
            'view' => ViewAccounts::route('/{record}'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            AccountsOverview::class,
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('filament-shield::filament-shield.nav.group');
    }
    public static function getNavigationLabel(): string
    {
        return __("Account");
    }

    public static function getBreadcrumb() : string
    {
        return __('Account');
    }
    public static function getModelLabel(): string
    {
        return __('Account');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Accounts');
    }

}
