<?php

namespace Modules\Account\app\Models;

use App\Models\UserAccountCredential;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Khaleds\Notifications\Traits\InteractWithNotifications;
use Laravel\Sanctum\HasApiTokens;
use Modules\BankAccount\Traits\HasBankAccounts;
use Modules\Company\app\Models\Company;
use Modules\Lease\app\Models\Lease;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\PropertyOwners;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Traits\HasRoles;
use App\Shared\HasRelationshipChecks;

/**
 * @property integer $id
 * @property string $name
 * @property string $gender
 * @property string $block_reason
 * @property string $lat
 * @property string $lng
 * @property string $email
 * @property string $phone
 * @property string $password
 * @property string $otp_code
 * @property string $otp_activated_at
 * @property string $last_login
 * @property boolean $is_first_login
 * @property boolean $is_active
 * @property boolean $is_blocked
 * @property string $cosed_reason
 * @property string $user_agent
 * @property string $user_ip
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 * @property AccountMeta[] $accountMetas
 *
 */
class Account extends Authenticatable implements HasMedia
{

    use HasFactory;
    use HasApiTokens;
    use InteractsWithMedia;
    use SoftDeletes;
    use HasRoles;
    use HasBankAccounts;
    use HasRelationshipChecks;

    use InteractWithNotifications;
    use Notifiable;
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $guarded = ['id'];

    protected $casts = [
        'is_blocked' => 'bool',
    ];

    protected $relationsList = ["properties", "leases"];
    protected $hidden = ['password'];

    protected function password(): Attribute
    {
        return Attribute::make(
            set: fn($value) => bcrypt($value)
        );
    }

    public function userAccountCredential()
    {
        return $this->hasOne(UserAccountCredential::class);
    }

    public function accountMetas()
    {
        return $this->hasMany('Modules\Account\app\Models\AccountMeta');
    }


    protected static function newFactory()
    {
        return AccountFactory::new();
    }

    public function getFullName(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function accountRoles()
    {
        return $this->hasMany('Modules\Account\app\Models\AccountRole');
    }

    // TODO check and edit this relationship to match the new scheme of the property owners table as morhp relationship not depends on user_id
    public function propertyOwner()
    {
        return $this->hasMany(PropertyOwners::class, 'user_id', 'id');
    }

    public function properties()
    {
        return $this->morphToMany(
            Property::class,
            'ownerable',
            'property_owners',
            'ownerable_id',
            'property_id'
        )->where('ownerable_type', Account::class);
    }

    /**
     * Get the leases associated with the account through lease_members
     */
    public function leases()
    {
        return $this->belongsToMany(
            Lease::class,
            'lease_members',
            'member_id',
            'lease_id'
        )->where('member_type', 'individual')
         ->withPivot([
             'member_role',
             'percentage',
             'bank_account_id'
         ])
         ->withTimestamps();
    }

    /**
     * Get the company that owns the account
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
